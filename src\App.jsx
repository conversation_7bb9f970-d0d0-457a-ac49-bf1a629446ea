import React, { Suspense } from "react";
import { Canvas } from "@react-three/fiber";
import { OrbitControls, Environment, Text3D } from "@react-three/drei";
import { Color } from "three";
import { Center } from "@react-three/drei";

function MetallicText3D() {
  return (
    <div style={{ width: "100vw", height: "100vh", background: "black" }}>
      <Canvas camera={{ position: [0, 0, 50], fov: 50 }} shadows>
        <ambientLight intensity={0.3} />
        <directionalLight
          position={[10, 10, 10]}
          intensity={1}
          castShadow
          shadow-mapSize-width={1024}
          shadow-mapSize-height={1024}
        />

        <Suspense fallback={null}>
          <Environment preset="city" />
          <Center>
            <Text3D
              font="/fonts/helvetiker_bold.typeface.json"
              size={10}
              height={3}
              curveSegments={12}
              bevelEnabled
              bevelThickness={0.8}
              bevelSize={0.4}
              bevelSegments={5}
            >
              NOVIX
              <meshStandardMaterial
                metalness={1}
                roughness={0.2}
                color={new Color("#cccccc")}
                emissive={new Color("#330000")}
                emissiveIntensity={0.4}
              />
            </Text3D>

            <Text3D
              position={[0, -15, 0]}
              font="/fonts/helvetiker_bold.typeface.json"
              size={5}
              height={2}
              curveSegments={12}
              bevelEnabled
              bevelThickness={0.4}
              bevelSize={0.2}
              bevelSegments={3}
            >
              STUDIOS
              <meshStandardMaterial
                metalness={1}
                roughness={0.2}
                color={new Color("#cccccc")}
                emissive={new Color("#330000")}
                emissiveIntensity={0.4}
              />
            </Text3D>
          </Center>
        </Suspense>

        <OrbitControls enableZoom={false} />
      </Canvas>
    </div>
  );
}

export default function App() {
  return <MetallicText3D />;
}
