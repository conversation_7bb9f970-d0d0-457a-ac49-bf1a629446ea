import Shape<PERSON>lur from './components/ShapeBlur'
import HyperspeedBackground from './components/HyperspeedBackground'
import './App.css'

function App() {
  return (
    <div className="relative w-full h-screen overflow-hidden bg-black flex flex-col items-center justify-center">
      {/* Hyperspeed Background */}
      <div className="absolute inset-0 z-0">
        <HyperspeedBackground preset="preset1" />
      </div>

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col items-center justify-center h-full">
        {/* Logo with ShapeBlur Background Effect */}
        <div className="relative flex items-center justify-center mb-16">
          {/* Sha<PERSON><PERSON>lur Effect positioned behind logo */}
          <div className="absolute w-[80vw] h-[40vh] max-w-[800px] max-h-[300px]">
            <ShapeBlur
              variation={0}
              shapeSize={0.9}
              roundness={0.1}
              borderSize={0.2}
              circleSize={0.6}
              circleEdge={0.3}
            />
          </div>

          {/* <PERSON>go positioned in center */}
          <div className="relative z-10">
            <img
              src="/logo.png"
              alt="Novix Studios"
              className="w-[70vw] h-auto max-w-[600px] object-contain"
              style={{
                filter: 'brightness(2) contrast(1.5) saturate(1.2) drop-shadow(0 0 30px rgba(255,255,255,0.8))',
                mixBlendMode: 'normal',
                background: 'transparent'
              }}
            />
          </div>
        </div>

        {/* Maintenance Text */}
        <div className="text-center">
          <h1 className="text-5xl md:text-7xl font-thin text-white tracking-widest">
            Under Maintenance
          </h1>
        </div>
      </div>
    </div>
  )
}

export default App
