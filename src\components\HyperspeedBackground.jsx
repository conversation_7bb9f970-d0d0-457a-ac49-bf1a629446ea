import { useRef, useEffect } from 'react';
import * as THREE from 'three';

// Hyperspeed presets
const hyperspeedPresets = {
  preset1: {
    roadWidth: 9,
    islandWidth: 2,
    lanesPerRoad: 3,
    roadSections: 3,
    speed: 80,
    length: 400,
    fov: 90,
    fovSpeedUp: 150,
    speedUp: 2,
    carLightsFade: 0.4,
    totalSideLightSticks: 50,
    lightPairsPerRoadWay: 50,
    shoulderLinesWidthWhite: 0.08,
    shoulderLinesWidthYellow: 0.06,
    brokenLinesWidthWhite: 0.14,
    brokenLinesLengthWhite: 1.5,
    brokenLinesLengthYellow: 1.5,
    lightStickWidth: [0.12, 0.5],
    lightStickHeight: [1.3, 1.7],
    movingAwaySpeed: [60, 80],
    movingCloserSpeed: [-120, -160],
    carLightsLength: [400 * 0.05, 400 * 0.15],
    carLightsRadius: [0.05, 0.14],
    carWidthPercentage: [0.3, 0.5],
    carShiftX: [-0.8, 0.8],
    carFloorSeparation: [0.05, 1],
    colors: {
      roadColor: 0x080808,
      islandColor: 0x0a0a0a,
      background: 0x000000,
      shoulderLines: 0xFFFFFF,
      brokenLines: 0xFFFFFF,
      leftCars: [0xff102a, 0xEB383E, 0xff102a],
      rightCars: [0xdadafa, 0xBEBAE3, 0x8F97E4],
      sticks: 0xdadafa,
    },
    distortion: {
      uniform: 'turbulentDistortion',
      value: new THREE.Vector2(0.02, 0.06)
    }
  },
  preset2: {
    roadWidth: 9,
    islandWidth: 2,
    lanesPerRoad: 3,
    roadSections: 3,
    speed: 80,
    length: 400,
    fov: 90,
    fovSpeedUp: 150,
    speedUp: 2,
    carLightsFade: 0.4,
    totalSideLightSticks: 50,
    lightPairsPerRoadWay: 50,
    shoulderLinesWidthWhite: 0.08,
    shoulderLinesWidthYellow: 0.06,
    brokenLinesWidthWhite: 0.14,
    brokenLinesLengthWhite: 1.5,
    brokenLinesLengthYellow: 1.5,
    lightStickWidth: [0.12, 0.5],
    lightStickHeight: [1.3, 1.7],
    movingAwaySpeed: [60, 80],
    movingCloserSpeed: [-120, -160],
    carLightsLength: [400 * 0.05, 400 * 0.15],
    carLightsRadius: [0.05, 0.14],
    carWidthPercentage: [0.3, 0.5],
    carShiftX: [-0.8, 0.8],
    carFloorSeparation: [0.05, 1],
    colors: {
      roadColor: 0x080808,
      islandColor: 0x0a0a0a,
      background: 0x000000,
      shoulderLines: 0xFFFFFF,
      brokenLines: 0xFFFFFF,
      leftCars: [0xff102a, 0xEB383E, 0xff102a],
      rightCars: [0xdadafa, 0xBEBAE3, 0x8F97E4],
      sticks: 0xdadafa,
    },
    distortion: {
      uniform: 'mountainDistortion',
      value: new THREE.Vector2(0.0002, 0.0001)
    }
  },
  preset3: {
    roadWidth: 9,
    islandWidth: 2,
    lanesPerRoad: 3,
    roadSections: 3,
    speed: 80,
    length: 400,
    fov: 90,
    fovSpeedUp: 150,
    speedUp: 2,
    carLightsFade: 0.4,
    totalSideLightSticks: 50,
    lightPairsPerRoadWay: 50,
    shoulderLinesWidthWhite: 0.08,
    shoulderLinesWidthYellow: 0.06,
    brokenLinesWidthWhite: 0.14,
    brokenLinesLengthWhite: 1.5,
    brokenLinesLengthYellow: 1.5,
    lightStickWidth: [0.12, 0.5],
    lightStickHeight: [1.3, 1.7],
    movingAwaySpeed: [60, 80],
    movingCloserSpeed: [-120, -160],
    carLightsLength: [400 * 0.05, 400 * 0.15],
    carLightsRadius: [0.05, 0.14],
    carWidthPercentage: [0.3, 0.5],
    carShiftX: [-0.8, 0.8],
    carFloorSeparation: [0.05, 1],
    colors: {
      roadColor: 0x080808,
      islandColor: 0x0a0a0a,
      background: 0x000000,
      shoulderLines: 0xFFFFFF,
      brokenLines: 0xFFFFFF,
      leftCars: [0xff102a, 0xEB383E, 0xff102a],
      rightCars: [0xdadafa, 0xBEBAE3, 0x8F97E4],
      sticks: 0xdadafa,
    },
    distortion: {
      uniform: 'xyDistortion',
      value: new THREE.Vector2(0.0002, 0.0001)
    }
  },
  preset4: {
    roadWidth: 9,
    islandWidth: 2,
    lanesPerRoad: 3,
    roadSections: 3,
    speed: 80,
    length: 400,
    fov: 90,
    fovSpeedUp: 150,
    speedUp: 2,
    carLightsFade: 0.4,
    totalSideLightSticks: 50,
    lightPairsPerRoadWay: 50,
    shoulderLinesWidthWhite: 0.08,
    shoulderLinesWidthYellow: 0.06,
    brokenLinesWidthWhite: 0.14,
    brokenLinesLengthWhite: 1.5,
    brokenLinesLengthYellow: 1.5,
    lightStickWidth: [0.12, 0.5],
    lightStickHeight: [1.3, 1.7],
    movingAwaySpeed: [60, 80],
    movingCloserSpeed: [-120, -160],
    carLightsLength: [400 * 0.05, 400 * 0.15],
    carLightsRadius: [0.05, 0.14],
    carWidthPercentage: [0.3, 0.5],
    carShiftX: [-0.8, 0.8],
    carFloorSeparation: [0.05, 1],
    colors: {
      roadColor: 0x080808,
      islandColor: 0x0a0a0a,
      background: 0x000000,
      shoulderLines: 0xFFFFFF,
      brokenLines: 0xFFFFFF,
      leftCars: [0xff102a, 0xEB383E, 0xff102a],
      rightCars: [0xdadafa, 0xBEBAE3, 0x8F97E4],
      sticks: 0xdadafa,
    },
    distortion: {
      uniform: 'LongRaceDistortion',
      value: new THREE.Vector2(0.0002, 0.0001)
    }
  },
  preset5: {
    roadWidth: 9,
    islandWidth: 2,
    lanesPerRoad: 3,
    roadSections: 3,
    speed: 80,
    length: 400,
    fov: 90,
    fovSpeedUp: 150,
    speedUp: 2,
    carLightsFade: 0.4,
    totalSideLightSticks: 50,
    lightPairsPerRoadWay: 50,
    shoulderLinesWidthWhite: 0.08,
    shoulderLinesWidthYellow: 0.06,
    brokenLinesWidthWhite: 0.14,
    brokenLinesLengthWhite: 1.5,
    brokenLinesLengthYellow: 1.5,
    lightStickWidth: [0.12, 0.5],
    lightStickHeight: [1.3, 1.7],
    movingAwaySpeed: [60, 80],
    movingCloserSpeed: [-120, -160],
    carLightsLength: [400 * 0.05, 400 * 0.15],
    carLightsRadius: [0.05, 0.14],
    carWidthPercentage: [0.3, 0.5],
    carShiftX: [-0.8, 0.8],
    carFloorSeparation: [0.05, 1],
    colors: {
      roadColor: 0x080808,
      islandColor: 0x0a0a0a,
      background: 0x000000,
      shoulderLines: 0xFFFFFF,
      brokenLines: 0xFFFFFF,
      leftCars: [0xff102a, 0xEB383E, 0xff102a],
      rightCars: [0xdadafa, 0xBEBAE3, 0x8F97E4],
      sticks: 0xdadafa,
    },
    distortion: {
      uniform: 'deepDistortion',
      value: new THREE.Vector2(0.0002, 0.0001)
    }
  },
  preset6: {
    roadWidth: 9,
    islandWidth: 2,
    lanesPerRoad: 3,
    roadSections: 3,
    speed: 80,
    length: 400,
    fov: 90,
    fovSpeedUp: 150,
    speedUp: 2,
    carLightsFade: 0.4,
    totalSideLightSticks: 50,
    lightPairsPerRoadWay: 50,
    shoulderLinesWidthWhite: 0.08,
    shoulderLinesWidthYellow: 0.06,
    brokenLinesWidthWhite: 0.14,
    brokenLinesLengthWhite: 1.5,
    brokenLinesLengthYellow: 1.5,
    lightStickWidth: [0.12, 0.5],
    lightStickHeight: [1.3, 1.7],
    movingAwaySpeed: [60, 80],
    movingCloserSpeed: [-120, -160],
    carLightsLength: [400 * 0.05, 400 * 0.15],
    carLightsRadius: [0.05, 0.14],
    carWidthPercentage: [0.3, 0.5],
    carShiftX: [-0.8, 0.8],
    carFloorSeparation: [0.05, 1],
    colors: {
      roadColor: 0x080808,
      islandColor: 0x0a0a0a,
      background: 0x000000,
      shoulderLines: 0xFFFFFF,
      brokenLines: 0xFFFFFF,
      leftCars: [0xff102a, 0xEB383E, 0xff102a],
      rightCars: [0xdadafa, 0xBEBAE3, 0x8F97E4],
      sticks: 0xdadafa,
    },
    distortion: {
      uniform: 'turbulentDistortion',
      value: new THREE.Vector2(0.02, 0.06)
    }
  }
};

const HyperspeedBackground = ({ preset = 'preset1', className = '' }) => {
  const mountRef = useRef();

  useEffect(() => {
    const mount = mountRef.current;
    if (!mount) return;

    // Basic Three.js setup for a simple animated background
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ alpha: true });
    
    renderer.setSize(mount.clientWidth, mount.clientHeight);
    renderer.setClearColor(hyperspeedPresets[preset].colors.background, 1);
    mount.appendChild(renderer.domElement);

    // Create a simple particle system for hyperspeed effect
    const particleCount = 1000;
    const particles = new THREE.BufferGeometry();
    const positions = new Float32Array(particleCount * 3);
    
    for (let i = 0; i < particleCount * 3; i += 3) {
      positions[i] = (Math.random() - 0.5) * 2000;     // x
      positions[i + 1] = (Math.random() - 0.5) * 2000; // y
      positions[i + 2] = Math.random() * 2000 - 1000;  // z
    }
    
    particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    
    const particleMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 2,
      transparent: true,
      opacity: 0.8
    });
    
    const particleSystem = new THREE.Points(particles, particleMaterial);
    scene.add(particleSystem);
    
    camera.position.z = 1;

    const animate = () => {
      requestAnimationFrame(animate);
      
      // Move particles towards camera for hyperspeed effect
      const positions = particleSystem.geometry.attributes.position.array;
      for (let i = 2; i < positions.length; i += 3) {
        positions[i] += hyperspeedPresets[preset].speed * 0.1;
        
        // Reset particle position when it passes the camera
        if (positions[i] > 1000) {
          positions[i] = -1000;
        }
      }
      
      particleSystem.geometry.attributes.position.needsUpdate = true;
      renderer.render(scene, camera);
    };

    const handleResize = () => {
      const width = mount.clientWidth;
      const height = mount.clientHeight;
      
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
      renderer.setSize(width, height);
    };

    window.addEventListener('resize', handleResize);
    animate();

    return () => {
      window.removeEventListener('resize', handleResize);
      mount.removeChild(renderer.domElement);
      renderer.dispose();
    };
  }, [preset]);

  return <div ref={mountRef} className={`w-full h-full ${className}`} />;
};

export default HyperspeedBackground;
